#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试过辊接触点计算功能
"""

import numpy as np
import matplotlib.pyplot as plt
from main import HexagonFilmSimulation

def test_tangent_calculation():
    """测试切线计算功能"""
    print("=" * 50)
    print("测试过辊接触点计算功能")
    print("=" * 50)
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0)
    
    # 测试几个不同的接触点
    test_points = [
        np.array([-30, 0]),    # V1位置
        np.array([30, 0]),     # V4位置
        np.array([20, 4]),     # V5位置
        np.array([2, 4]),      # 上方点位置
        np.array([10, 20]),    # 随机点
    ]
    
    print(f"过辊圆心位置: ({sim.A[0]:.1f}, {sim.A[1]:.1f})")
    print(f"过辊半径: {sim.roller_radius:.1f}")
    print()
    
    for i, contact_point in enumerate(test_points):
        print(f"测试点 {i+1}: ({contact_point[0]:.1f}, {contact_point[1]:.1f})")
        
        # 计算切点
        tangent_points = sim.calc_tangent_points(
            sim.A[0], sim.A[1], sim.roller_radius, contact_point
        )
        
        print(f"  找到 {len(tangent_points)} 个切点:")
        for j, tp in enumerate(tangent_points):
            print(f"    切点 {j+1}: ({tp[0]:.2f}, {tp[1]:.2f})")
            
            # 验证切点是否在圆上
            dist_to_center = np.linalg.norm(tp - sim.A)
            print(f"      到圆心距离: {dist_to_center:.3f} (应该等于 {sim.roller_radius:.1f})")
            
            # 验证切线条件
            to_tangent = tp - contact_point
            to_center = sim.A - tp
            dot_product = np.dot(to_tangent, to_center)
            print(f"      切线验证 (点乘应该≈0): {dot_product:.6f}")
        
        # 使用find_roller_contact_point函数
        roller_contact = sim.find_roller_contact_point(contact_point)
        print(f"  选择的过辊接触点: ({roller_contact[0]:.2f}, {roller_contact[1]:.2f})")
        print(f"  是否在左侧 (x>0.5): {roller_contact[0] > 0.5}")
        print(f"  切线长度: {np.linalg.norm(contact_point - roller_contact):.2f}")
        print()

def visualize_roller_contact():
    """可视化过辊接触点计算"""
    print("创建可视化图表...")
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0)
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 10))
    ax.set_aspect('equal')
    ax.grid(True, alpha=0.3)
    ax.set_title('过辊接触点计算演示', fontsize=16, fontweight='bold')
    
    # 绘制过辊圆
    roller_circle = plt.Circle((sim.A[0], sim.A[1]), sim.roller_radius, 
                              fill=False, color='blue', linewidth=3, label='过辊圆')
    ax.add_patch(roller_circle)
    
    # 绘制过辊圆心
    ax.plot(sim.A[0], sim.A[1], 'bo', markersize=12, 
            markerfacecolor='blue', markeredgecolor='darkblue', 
            markeredgewidth=2, label='过辊圆心')
    
    # 绘制初始六边形
    hex_x = np.append(sim.vertices[:, 0], sim.vertices[0, 0])
    hex_y = np.append(sim.vertices[:, 1], sim.vertices[0, 1])
    ax.plot(hex_x, hex_y, 'k-', linewidth=2, label='六边形卷针')
    
    # 标注顶点
    for i, vertex in enumerate(sim.vertices):
        ax.plot(vertex[0], vertex[1], 'ko', markersize=8)
        ax.text(vertex[0] + 1, vertex[1] + 1, f'V{i+1}', fontsize=10, fontweight='bold')
    
    # 测试几个接触点
    test_points = [
        (np.array([-30, 0]), 'V1', 'red'),
        (np.array([30, 0]), 'V4', 'orange'),
        (np.array([20, 4]), 'V5', 'purple'),
        (np.array([2, 4]), '上方点', 'magenta'),
    ]
    
    for contact_point, label, color in test_points:
        # 绘制接触点
        ax.plot(contact_point[0], contact_point[1], 'o', 
                color=color, markersize=10, markerfacecolor=color,
                markeredgecolor='black', markeredgewidth=1)
        ax.text(contact_point[0] + 2, contact_point[1] + 2, label, 
                fontsize=10, fontweight='bold', color=color)
        
        # 计算并绘制过辊接触点
        roller_contact = sim.find_roller_contact_point(contact_point)
        ax.plot(roller_contact[0], roller_contact[1], 's', 
                color=color, markersize=8, markerfacecolor=color,
                markeredgecolor='black', markeredgewidth=1, alpha=0.7)
        
        # 绘制切线
        ax.plot([contact_point[0], roller_contact[0]], 
                [contact_point[1], roller_contact[1]], 
                '--', color=color, linewidth=2, alpha=0.8)
        
        # 标注切线长度
        tangent_length = np.linalg.norm(contact_point - roller_contact)
        mid_x = (contact_point[0] + roller_contact[0]) / 2
        mid_y = (contact_point[1] + roller_contact[1]) / 2
        ax.text(mid_x, mid_y - 2, f'{tangent_length:.1f}', 
                fontsize=9, color=color, fontweight='bold',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    # 添加说明
    ax.text(0.02, 0.98, 
            '说明:\n• 圆形: 接触点\n• 方形: 过辊接触点\n• 虚线: 切线路径\n• 数字: 切线长度', 
            transform=ax.transAxes, fontsize=12, verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.9))
    
    ax.set_xlim(-40, 40)
    ax.set_ylim(-10, 90)
    ax.legend()
    
    plt.tight_layout()
    plt.show()

def test_simulation_with_roller_contact():
    """测试包含过辊接触点的仿真"""
    print("运行包含过辊接触点的仿真测试...")
    
    # 创建仿真对象
    sim = HexagonFilmSimulation(rotation_center_x_offset=2.0)
    
    # 运行短距离仿真用于测试
    sim.total_rotation = 180  # 只旋转180度
    sim.theta_deg = np.arange(0, 181, 5)  # 每5度一个点
    sim.theta = np.deg2rad(sim.theta_deg)
    
    # 重新初始化数组
    sim.L = np.zeros(len(sim.theta))
    sim.S = np.zeros(len(sim.theta))
    sim.contact_points = np.zeros((len(sim.theta), 2))
    sim.contact_type = [''] * len(sim.theta)
    sim.roller_contact_points = np.zeros((len(sim.theta), 2))
    sim.roller_contact_distances = np.zeros(len(sim.theta))
    sim.upper_point_trajectory = np.zeros((len(sim.theta), 2))
    sim.lower_point_trajectory = np.zeros((len(sim.theta), 2))
    sim.layer_numbers = np.zeros(len(sim.theta), dtype=int)
    sim.accumulated_thickness = np.zeros(len(sim.theta))
    sim.layer_consumption = np.zeros(len(sim.theta))
    
    # 运行仿真
    sim.run_simulation()
    
    # 显示结果
    print(f"仿真完成! 共计算了 {len(sim.theta)} 个角度点")
    print(f"切线长度统计:")
    print(f"  最大: {np.max(sim.roller_contact_distances):.2f}")
    print(f"  最小: {np.min(sim.roller_contact_distances):.2f}")
    print(f"  平均: {np.mean(sim.roller_contact_distances):.2f}")
    print(f"  标准差: {np.std(sim.roller_contact_distances):.2f}")
    
    # 检查过辊接触点是否都在左侧
    left_side_count = np.sum(sim.roller_contact_points[:, 0] > 0.5)
    print(f"在左侧的过辊接触点数量: {left_side_count}/{len(sim.theta)} ({left_side_count/len(sim.theta)*100:.1f}%)")

if __name__ == "__main__":
    # 运行测试
    test_tangent_calculation()
    
    input("按回车键继续可视化演示...")
    visualize_roller_contact()
    
    input("按回车键继续仿真测试...")
    test_simulation_with_roller_contact()
    
    print("\n测试完成!")
